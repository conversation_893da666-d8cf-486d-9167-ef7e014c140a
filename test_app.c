#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "sqlite3.h"

int main() {
    sqlite3* db;
    int rc = sqlite3_open("students.db", &db);

    if (rc) {
        fprintf(stderr, "Failed to open database: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    // Create students table if it doesn't exist
    const char* createTableSQL = "CREATE TABLE IF NOT EXISTS students ("
                                 "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                                 "name TEXT NOT NULL, "
                                 "age INTEGER NOT NULL);";
    char* errMsg = NULL;
    rc = sqlite3_exec(db, createTableSQL, NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Error creating table: %s\n", errMsg);
        sqlite3_free(errMsg);
    }

    // Insert student data
    char name[256];
    int age;
    printf("Enter student name: ");
    fgets(name, sizeof(name), stdin);
    // Remove newline from end of name
    name[strcspn(name, "\n")] = 0;
    
    printf("Enter student age: ");
    scanf("%d", &age);

    char insertSQL[512];
    snprintf(insertSQL, sizeof(insertSQL), "INSERT INTO students (name, age) VALUES ('%s', %d);", name, age);
    rc = sqlite3_exec(db, insertSQL, NULL, NULL, &errMsg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "Error inserting data: %s\n", errMsg);
        sqlite3_free(errMsg);
    }

    // Display data
    const char* selectSQL = "SELECT id, name, age FROM students;";
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, selectSQL, -1, &stmt, NULL);

    if (rc == SQLITE_OK) {
        printf("\n--- Student List ---\n");
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            int id = sqlite3_column_int(stmt, 0);
            const unsigned char* name = sqlite3_column_text(stmt, 1);
            int age = sqlite3_column_int(stmt, 2);

            printf("ID: %d, Name: %s, Age: %d\n", id, name, age);
        }
        sqlite3_finalize(stmt);
    } else {
        fprintf(stderr, "Failed to read data: %s\n", sqlite3_errmsg(db));
    }

    sqlite3_close(db);
    return 0;
}
