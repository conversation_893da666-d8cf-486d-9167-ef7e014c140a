cmake_minimum_required(VERSION 3.10)
project(StudentDatabase)

set(CMAKE_CXX_STANDARD 17)
set(CMAKE_CXX_STANDARD_REQUIRED ON)

# البحث عن SQLite3
find_package(PkgConfig REQUIRED)
pkg_check_modules(SQLITE3 REQUIRED sqlite3)

# إضافة المصدر
add_executable(student_db main.cpp)

# ربط SQLite3
target_link_libraries(student_db ${SQLITE3_LIBRARIES})
target_include_directories(student_db PRIVATE ${SQLITE3_INCLUDE_DIRS})
target_compile_options(student_db PRIVATE ${SQLITE3_CFLAGS_OTHER})

# إعدادات إضافية لـ Windows
if(WIN32)
    target_link_libraries(student_db sqlite3)
endif()
