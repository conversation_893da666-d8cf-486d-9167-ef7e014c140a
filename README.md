# برنامج إدارة الطلاب - Student Database

برنامج بسيط لإدارة بيانات الطلاب باستخدام C++ و SQLite.

## المتطلبات

### Windows:
- MinGW-w64 أو Visual Studio
- SQLite3 library

### Linux/macOS:
- g++ compiler
- SQLite3 development package

## تثبيت المتطلبات

### Ubuntu/Debian:
```bash
sudo apt update
sudo apt install build-essential libsqlite3-dev
```

### CentOS/RHEL:
```bash
sudo yum install gcc-c++ sqlite-devel
```

### macOS:
```bash
brew install sqlite3
```

### Windows:
1. تثبيت MinGW-w64
2. تحميل SQLite3 من الموقع الرسمي
3. إضافة المكتبات إلى مسار النظام

## طرق التشغيل

### الطريقة الأولى - استخدام Makefile:
```bash
make
make run
```

### الطريقة الثانية - استخدام CMake:
```bash
mkdir build
cd build
cmake ..
make
./student_db
```

### الطريقة الثالثة - التشغيل المباشر:
```bash
g++ -std=c++17 -o student_db main.cpp -lsqlite3
./student_db
```

## الميزات

- إنشاء قاعدة بيانات SQLite تلقائياً
- إدخال بيانات الطلاب (الاسم والعمر)
- عرض قائمة جميع الطلاب
- حماية من SQL Injection باستخدام Prepared Statements
- دعم النصوص العربية

## التحسينات المضافة

1. **استخدام Prepared Statements**: لتجنب SQL Injection
2. **تنظيف Input Buffer**: لضمان قراءة صحيحة للنصوص
3. **معالجة أفضل للأخطاء**: إغلاق قاعدة البيانات عند حدوث خطأ
4. **دعم أفضل للنصوص العربية**: إعداد الترميز المناسب

## استكشاف الأخطاء

إذا واجهت مشاكل في التشغيل:

1. تأكد من تثبيت SQLite3 development package
2. تحقق من وجود المكتبات في مسار النظام
3. في Windows، قد تحتاج لتحديد مسار المكتبات يدوياً
