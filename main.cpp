#include <stdio.h>
#include <stdlib.h>
#include <string.h>
#include "sqlite3.h"

int main() {
    sqlite3* db;
    int rc = sqlite3_open("students.db", &db);

    if (rc) {
        fprintf(stderr, "فشل في فتح قاعدة البيانات: %s\n", sqlite3_errmsg(db));
        return 1;
    }

    // إنشاء جدول الطلاب إن لم يكن موجودًا
    const char* createTableSQL = "CREATE TABLE IF NOT EXISTS students ("
                                 "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                                 "name TEXT NOT NULL, "
                                 "age INTEGER NOT NULL);";
    char* errMsg = nullptr;
    rc = sqlite3_exec(db, createTableSQL, nullptr, nullptr, &errMsg);
    if (rc != SQLITE_OK) {
        fprintf(stderr, "خطأ في إنشاء الجدول: %s\n", errMsg);
        sqlite3_free(errMsg);
    }

    // إدخال بيانات طالب
    std::string name;
    int age;
    std::cout << "ادخل اسم الطالب: ";
    std::getline(std::cin, name);
    std::cout << "ادخل عمر الطالب: ";
    std::cin >> age;

    std::string insertSQL = "INSERT INTO students (name, age) VALUES ('" + name + "', " + std::to_string(age) + ");";
    rc = sqlite3_exec(db, insertSQL.c_str(), nullptr, nullptr, &errMsg);
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في إدخال البيانات: " << errMsg << std::endl;
        sqlite3_free(errMsg);
    }

    // عرض البيانات
    const char* selectSQL = "SELECT id, name, age FROM students;";
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, selectSQL, -1, &stmt, nullptr);

    if (rc == SQLITE_OK) {
        std::cout << "\n--- قائمة الطلاب ---\n";
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            int id = sqlite3_column_int(stmt, 0);
            const unsigned char* name = sqlite3_column_text(stmt, 1);
            int age = sqlite3_column_int(stmt, 2);

            std::cout << "ID: " << id << ", الاسم: " << name << ", العمر: " << age << std::endl;
        }
        sqlite3_finalize(stmt);
    } else {
        std::cerr << "فشل في قراءة البيانات: " << sqlite3_errmsg(db) << std::endl;
    }

    sqlite3_close(db);
    return 0;
}
