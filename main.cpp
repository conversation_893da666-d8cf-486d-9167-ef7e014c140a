#include <iostream>
#include <string>
#include <locale>
#include <codecvt>
#include "sqlite3.h"

int main() {
    // تعيين الترميز للنص العربي
    std::locale::global(std::locale(""));
    std::wcout.imbue(std::locale(""));
    
    sqlite3* db;
    int rc = sqlite3_open("students.db", &db);

    if (rc) {
        std::cerr << "فشل في فتح قاعدة البيانات: " << sqlite3_errmsg(db) << std::endl;
        sqlite3_close(db);
        return 1;
    }

    // إنشاء جدول الطلاب إن لم يكن موجودًا
    const char* createTableSQL = "CREATE TABLE IF NOT EXISTS students ("
                                 "id INTEGER PRIMARY KEY AUTOINCREMENT, "
                                 "name TEXT NOT NULL, "
                                 "age INTEGER NOT NULL);";
    char* errMsg = nullptr;
    rc = sqlite3_exec(db, createTableSQL, nullptr, nullptr, &errMsg);
    if (rc != SQLITE_OK) {
        std::cerr << "خطأ في إنشاء الجدول: " << errMsg << std::endl;
        sqlite3_free(errMsg);
        sqlite3_close(db);
        return 1;
    }

    // إدخال بيانات طالب
    std::string name;
    int age;
    
    std::cout << "ادخل اسم الطالب: ";
    std::cin.ignore(); // تنظيف buffer
    std::getline(std::cin, name);
    
    std::cout << "ادخل عمر الطالب: ";
    std::cin >> age;

    // استخدام prepared statement لتجنب SQL injection
    const char* insertSQL = "INSERT INTO students (name, age) VALUES (?, ?);";
    sqlite3_stmt* insertStmt;
    rc = sqlite3_prepare_v2(db, insertSQL, -1, &insertStmt, nullptr);
    
    if (rc == SQLITE_OK) {
        sqlite3_bind_text(insertStmt, 1, name.c_str(), -1, SQLITE_STATIC);
        sqlite3_bind_int(insertStmt, 2, age);
        
        rc = sqlite3_step(insertStmt);
        if (rc != SQLITE_DONE) {
            std::cerr << "خطأ في إدخال البيانات: " << sqlite3_errmsg(db) << std::endl;
        } else {
            std::cout << "تم إدخال البيانات بنجاح!" << std::endl;
        }
        sqlite3_finalize(insertStmt);
    } else {
        std::cerr << "فشل في تحضير استعلام الإدخال: " << sqlite3_errmsg(db) << std::endl;
    }

    // عرض البيانات
    const char* selectSQL = "SELECT id, name, age FROM students;";
    sqlite3_stmt* stmt;
    rc = sqlite3_prepare_v2(db, selectSQL, -1, &stmt, nullptr);

    if (rc == SQLITE_OK) {
        std::cout << "\n--- قائمة الطلاب ---\n";
        while (sqlite3_step(stmt) == SQLITE_ROW) {
            int id = sqlite3_column_int(stmt, 0);
            const unsigned char* nameFromDB = sqlite3_column_text(stmt, 1);
            int ageFromDB = sqlite3_column_int(stmt, 2);

            std::cout << "ID: " << id << ", الاسم: " << nameFromDB << ", العمر: " << ageFromDB << std::endl;
        }
        sqlite3_finalize(stmt);
    } else {
        std::cerr << "فشل في قراءة البيانات: " << sqlite3_errmsg(db) << std::endl;
    }

    sqlite3_close(db);
    return 0;
}
